import { ScaledSheet } from 'react-native-size-matters';

export const styles = ScaledSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFCFF',
  },
  scrollView: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'stretch',
    backgroundColor: '#FAFBFCFF',
    borderRadius: '10@s',
    marginTop: '10%',
    marginBottom: '5%',
    marginHorizontal: '15@s',
    paddingHorizontal: '16@s',
    paddingVertical: '12@vs',
    gap: '8@vs',
    width: 'auto',
  },
  modalHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  editCardHeading: {
    fontSize: '16@ms',
    color: '#000',
    fontWeight: '500',
  },
  horizontalLine: {
    backgroundColor: '#BCBEC0',
    height: 1,
    width: '100%',
    alignSelf: 'center',
    marginVertical: '4%',
  },
  modalPickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '12@vs',
    paddingHorizontal: '8@s',
    gap: '6@s',
    width: '100%',
    
    
  },
  modalPickerLabel: {
    fontWeight: '500',
    fontSize: '14@ms',
    color: '#000',
    flex: 0,
    minWidth: '70@s',
    maxWidth: '85@s',
    textAlign: 'left',
  },
  modalPicker: {
    height: '36@vs',
    flex: 1,
    minWidth: '120@s',
    maxWidth: '180@s',
    color: '#000',
    borderRadius: '10@s',
  },
  deleteButtonContainer: {
    flex: 0,
    padding: '6@s',
    borderRadius: '4@s',
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: '32@s',
    minHeight: '32@s',
  },
  modalPickerDelete: {
    width: '18@s',
    height: '18@s',
  },
  addSportContainer: {
    marginVertical: '8@vs',
    alignItems: 'center',
  },
  addSportButton: {
    backgroundColor: '#fff',
    borderWidth: '1.5@s',
    borderColor: '#000',
    borderRadius: '5@s',
    paddingVertical: '10@vs',
    paddingHorizontal: '16@s',
    minHeight: '40@vs',
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: '150@s',
    maxWidth: '200@s',
    alignSelf: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  addSportButtonText: {
    color: '#000',
    fontWeight: '500',
    fontSize: '14@ms',
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: '8@s',
    flexWrap: 'wrap',
    width: '100%',
    marginTop: '16@vs',
  },
  saveButton: {
    backgroundColor: '#000',
    borderRadius: '6@s',
    paddingVertical: '12@vs',
    paddingHorizontal: '20@s',
    flex: 1,
    marginRight: '4@s',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '44@vs',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: '14@ms',
    fontWeight: '600',
    textAlign: 'center',
  },
  closeButton: {
    backgroundColor: '#fff',
    borderRadius: '6@s',
    borderWidth: '1.5@s',
    borderColor: '#000',
    paddingVertical: '12@vs',
    paddingHorizontal: '20@s',
    flex: 1,
    marginLeft: '4@s',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '44@vs',
  },
  closeButtonText: {
    color: '#000',
    fontSize: '14@ms',
    fontWeight: '600',
    textAlign: 'center',
  },
});
