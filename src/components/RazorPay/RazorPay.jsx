import React, { useState, useEffect, useCallback } from 'react';
import { Button, Alert, Text, TouchableOpacity, View, } from 'react-native';
import RazorpayCheckout from 'react-native-razorpay';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from '@react-native-community/checkbox';

import { NEXT_PUBLIC_BASE_URL, RAZORPAY_KEY, NEXT_WALLET_URL } from '@env';
import { useNavigation, useIsFocused, useFocusEffect } from '@react-navigation/native';
import { useRoute } from '@react-navigation/native';
import moment from "moment-timezone";
import { useAuth } from '../../Context/AuthContext';
import TermsAndConditionModal from '../TermsAndConditionModal';

export default function RazorPay({ data, total, playerData, bookingArray, setSelectedSlots, setModifiedSlots }) {
  const navigation = useNavigation();
  const [bookingId, setBookingID] = useState();
  const { isLoggedIn, userId , userToken } = useAuth();
  const isFocused = useIsFocused();
  const [isWalletChecked, setIsWalletChecked] = useState(false);
  const [isAlreadyBooked, setIsAlreadyBooked] = useState(true);
  const [walletPriceZero, setWalletPriceZero] = useState(false)
  const [walletPrice, setWalletPrice] = useState(0);
  const handleWalletCheckboxChange = () => {
    setIsWalletChecked(!isWalletChecked);
  };
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);
  const [privacyPolicyAccepted, setPrivacyPolicyAccepted] = useState(false);
  
  // Reset privacy policy acceptance when course changes
  useEffect(() => {
    setPrivacyPolicyAccepted(false);
  }, [data._id]);
  
  useFocusEffect(
    useCallback(() => {
      async function fetchWalletData() {
        try {
          console.log('Fetching wallet data for email:', playerData?.email);
          console.log('Using token:', userToken ? 'Token exists' : 'No token');
          console.log('API URL:', `${NEXT_WALLET_URL}/api/wallet?email=${playerData?.email}`);

          const requestOptions = {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${userToken}`,
            },
          };

          // Use the same endpoint structure as the working web version
          const response = await fetch(`${NEXT_WALLET_URL}/api/wallet?email=${playerData?.email}`, requestOptions);

          console.log('Response status:', response.status);
          console.log('Response ok:', response.ok);

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          console.log('Wallet API Response:', result);

          // Handle response same as web version
          if (result.data && result.data.length > 0) {
            const balance = result.data[0].balance || 0;
            console.log('Setting wallet balance to:', balance);
            setWalletPrice(balance);
          } else {
            console.log('No wallet data found in response');
            setWalletPrice(0);
          }
        } catch (error) {
          console.error("Error fetching wallet data:", error);
          setWalletPrice(0);
        }
      }
      const requestOptions = {
        method: "GET",
        redirect: "follow",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userToken}`,
        },
      };

      fetch(`${NEXT_PUBLIC_BASE_URL}/api/booking/?playerId=${userId}&courseId=${data._id}`, requestOptions)
        .then((response) => response.json())
        .then((result) => {
          setIsAlreadyBooked(result.data)
        })
        .catch((error) => console.error(error));

      if (userId && userToken && playerData?.email) {
        fetchWalletData();
      }
      return () => {
        // Don't reset wallet price on cleanup to maintain state
      };
    }, [userId, userToken, playerData?.email])
  );
  const handleRazorPay = async () => {
    if (!privacyPolicyAccepted){
      setIsTermsModalOpen(true); 
      return;
    }
    await handlePaymentProcess();
  };
  const handleAcceptPolicy = async() => {
    setIsTermsModalOpen(false);
    setPrivacyPolicyAccepted(true);
    await handlePaymentProcess();
  };

  const handlePaymentProcess = async() => {
    if (!isLoggedIn) {
      Alert.alert("Authentication", "Please sign in to book a session.", [
        { text: "OK", onPress: () => navigation.navigate('SignIn') } //
      ]);
    } else {
      try {
        const SessionEvents = bookingArray.map(x => {
          const startTime = convertTimeTo24HourFormat(x.start);
          const endTime = convertTimeTo24HourFormat(x.end);
          return {
            date: moment.tz(`${x.date}T${startTime}:00.000`, "Asia/Kolkata").format(),
            duration: x.duration,
            days: x.days,
            startTime: startTime,
            endTime: endTime,
            fees: x.fees
          };
        });
        let raw = {
          pricePaid: total,
          currency: "INR",
          coachId: data?.coach_id?._id,
          courseId: data?._id,
          player: userId,
          courseType: data?.classType,
          groupSize: data?.maxGroupSize,
          wallet: isWalletChecked,
        };
        if (data?.classType === "class") {
          raw.classes = SessionEvents;
        }
        // Use context values only, no AsyncStorage
        if (!userToken || !userId) {
          Alert.alert('Session expired', 'Please log in again.');
          return;
        }
        let requestOptions = {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${userToken}`,
          },
          redirect: 'follow',
        };
        const playerResponse = await fetch(
          `${NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
          requestOptions,
        );
        if (!playerResponse.ok) {
          const text = await playerResponse.text();
          console.error(
            `Player response error. Status: ${playerResponse.status}, Body: ${text}`,
          );
          Alert.alert('Error', 'Failed to verify user information.');
          return;
        }
        const playerResult = await playerResponse.json();
        if (playerResult) {
          const paymentInitiationOptions = {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${userToken}`,
            },
            body: JSON.stringify(raw),
            redirect: 'follow',
          };
          const paymentResponse = await fetch(
            `${NEXT_PUBLIC_BASE_URL}/api/payments/checkout`,
            paymentInitiationOptions,
          );
          const paymentResult = await paymentResponse.json();
          if(paymentResult?.error){
            Alert.alert('Can not Book', (paymentResult?.error) )
          }
          else if (paymentResult?.data?.order == "Not Required") {
            setSelectedSlots([]);
            setModifiedSlots([]);
            navigation.navigate('ThankYou', { BookingId: paymentResult?.booking?.data?._id });
          }
          else if (paymentResult && paymentResult.data && paymentResult.data.id) {
            var options = {
              key: RAZORPAY_KEY,
              amount: total.toFixed(2),
              currency: 'INR',
              name: 'KhelSports',
              description: `${data.courseName} ${data.classType} Transaction`,
              image: data?.images[0]?.url,
              order_id: paymentResult.data.id,
              prefill: {
                name: `${playerData.firstName} ${playerData.lastName}`,
                email: playerData.email,
                contact: playerData.mobile,
              },
              notes: {
                address: 'Razorpay Corporate Office',
              },
              theme: {
                color: '#3399cc',
              },
            };

            RazorpayCheckout.open(options)
              .then(async paymentData => {
                const requestOptions1 = {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${userToken}`,
                  },
                  body: JSON.stringify(paymentData),
                  redirect: 'follow',
                };

                try {
                  const paymentVerification = await fetch(
                    `${NEXT_PUBLIC_BASE_URL}/api/payments/paymentVerification`,
                    requestOptions1,
                  );
                  const verifyResult = await paymentVerification.json();
                  setBookingID(verifyResult.bookingId)
                  setSelectedSlots([]);
                  setModifiedSlots([]);
                  navigation.navigate('ThankYou', { BookingId: verifyResult._id });
                } catch (error) {
                  console.error('Error during payment verification:', error);
                  // Handle error accordingly
                }
              })
              .catch(error => {
                console.error('Razorpay Checkout Error:', error);
              });
          } else {
            Alert.alert('Error', 'Payment data is invalid.');
          }
        } else {
          Alert.alert('Error', 'User verification failed.');
        }
      } catch (error) {
        console.error('Error during payment initialization:', error);
        Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      }
    }
  };

  const convertTimeTo24HourFormat = (time) => {
    const [hourMinute, period] = time.split(/\s+/);
    const [hour, minute] = hourMinute.split(":");
    let newHour = parseInt(hour);
    if (period === "PM" && newHour !== 12) {
      newHour += 12;
    } else if (period === "AM" && newHour === 12) {
      newHour = 0;
    }
    console.log("--------------------> newHour:", `${newHour.toString().padStart(2, "0")}`)

    console.log("--------------------> newMinute:", `${minute.replace(
      /\D/g,
      ""
    )}`)
    return `${newHour.toString().padStart(2, "0")}:${minute.replace(
      /\D/g,
      ""
    )}`;
  };
  return <>
  {isTermsModalOpen && (
          <TermsAndConditionModal
            open={isTermsModalOpen}
            setOpen={setIsTermsModalOpen}
            saveData={{ handleSubmit: handleAcceptPolicy }}
            playerData={{...playerData, privacyPolicyAccepted: privacyPolicyAccepted}}
          />
        )}
    <View>
      <TouchableOpacity
        onPress={handleWalletCheckboxChange}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          opacity: walletPrice === 0 ? 0.4 : 1, 
        }}
        disabled={walletPrice === 0} 
      >
        <CheckBox
        disabled={walletPrice === 0} 
          value={isWalletChecked}
          onValueChange={handleWalletCheckboxChange}
          style={{ marginRight: "1%" }}
          tintColors={{ true: 'black', false: 'black' }}
        />
        <Text style={{ color: "#000", fontSize: 14 }}>Use wallet: ₹{walletPrice.toFixed(2)}</Text>
      </TouchableOpacity>
    </View>
    {/* <TouchableOpacity onPress={handleRazorPay} >
      <View style={{ width: "100%", height: 44, borderRadius: 3, backgroundColor: "#E31F26", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <Text style={{ fontSize: 16, color: "#fff", }} onPress={handleRazorPay} >BOOK NOW</Text>
      </View>
    </TouchableOpacity> */}
    {data.classType == "course" ? data?.playerEnrolled == data?.maxGroupSize || isAlreadyBooked?.length > 0 ?
      <View style={{ width: "100%", height: 44, borderRadius: 3, backgroundColor: "#E31F26", display: "flex", justifyContent: "center", alignItems: "center", opacity: 0.6, }}>
        <Text style={{ fontSize: 16, color: "#fff", }}>SLOTS UNAVAILABLE</Text>
      </View> :
      <TouchableOpacity onPress={handleRazorPay} >
        <View style={{ width: "100%", height: 44, borderRadius: 3, backgroundColor: "#E31F26", display: "flex", justifyContent: "center", alignItems: "center" }}>
          <Text style={{ fontSize: 16, color: "#fff", }} onPress={handleRazorPay} >BOOK NOW</Text>
        </View>
      </TouchableOpacity> :
       <TouchableOpacity onPress={handleRazorPay} >
      <View style={{ width: "100%", height: 44, borderRadius: 3, backgroundColor: "#E31F26", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <Text style={{ fontSize: 16, color: "#fff", }} onPress={handleRazorPay} >BOOK NOW</Text>
      </View>
    </TouchableOpacity>}
  </>

}
